import numpy as np
from typing import Tuple, List, Optional

class OBJLoader:
    """简单的OBJ文件加载器，支持顶点、纹理坐标和面"""
    
    def __init__(self):
        self.vertices = []
        self.texture_coords = []
        self.faces = []
        self.face_texture_indices = []
        
    def load(self, filepath: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        加载OBJ文件
        
        Returns:
            vertices: 顶点数组 (N, 3)
            texture_coords: 纹理坐标数组 (M, 2)
            indices: 面索引数组 (F, 3)
        """
        self.vertices = []
        self.texture_coords = []
        self.faces = []
        self.face_texture_indices = []
        
        with open(filepath, 'r') as file:
            for line in file:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                    
                parts = line.split()
                if not parts:
                    continue
                    
                if parts[0] == 'v':
                    # 顶点坐标
                    vertex = [float(parts[1]), float(parts[2]), float(parts[3])]
                    self.vertices.append(vertex)
                    
                elif parts[0] == 'vt':
                    # 纹理坐标
                    tex_coord = [float(parts[1]), float(parts[2])]
                    self.texture_coords.append(tex_coord)
                    
                elif parts[0] == 'f':
                    # 面信息 (支持 v/vt/vn 格式)
                    face_vertices = []
                    face_tex_coords = []
                    
                    for vertex_data in parts[1:]:
                        indices = vertex_data.split('/')
                        # 顶点索引 (OBJ文件索引从1开始，转换为从0开始)
                        vertex_idx = int(indices[0]) - 1
                        face_vertices.append(vertex_idx)
                        
                        # 纹理坐标索引
                        if len(indices) > 1 and indices[1]:
                            tex_idx = int(indices[1]) - 1
                            face_tex_coords.append(tex_idx)
                    
                    # 只处理三角形面
                    if len(face_vertices) == 3:
                        self.faces.append(face_vertices)
                        if len(face_tex_coords) == 3:
                            self.face_texture_indices.append(face_tex_coords)
        
        # 转换为numpy数组
        vertices = np.array(self.vertices, dtype=np.float32)
        texture_coords = np.array(self.texture_coords, dtype=np.float32) if self.texture_coords else np.array([], dtype=np.float32).reshape(0, 2)
        indices = np.array(self.faces, dtype=np.uint32).flatten()
        
        return vertices, texture_coords, indices
    
    def create_vertex_buffer_with_texcoords(self, vertices_0: np.ndarray, vertices_1: np.ndarray, texture_coords: np.ndarray, indices: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建包含顶点和纹理坐标的顶点缓冲区
        
        Returns:
            vertex_buffer: 交错的顶点数据 [x, y, z, x_rec, y_rec, z_rec, u, v, ...]
            indices: 索引数组
        """
        if len(texture_coords) == 0 or len(self.face_texture_indices) == 0:
            # 如果没有纹理坐标，创建默认的纹理坐标
            vertex_buffer = []
            for i in range(len(vertices_0)):
                vertex_buffer.extend([vertices_0[i][0], vertices_0[i][1], vertices_0[i][2], vertices_1[i][0], vertices_1[i][1], vertices_1[i][2], 0.0, 0.0])
            return np.array(vertex_buffer, dtype=np.float32), indices
        
        # 创建新的顶点缓冲区，每个顶点包含位置和纹理坐标
        vertex_buffer = []
        new_indices = []
        vertex_map = {}  # 用于去重 (vertex_idx, tex_idx) -> new_vertex_idx
        
        for face_idx, face in enumerate(self.faces):
            face_indices = []
            tex_indices = self.face_texture_indices[face_idx] if face_idx < len(self.face_texture_indices) else [0, 0, 0]
            
            for i in range(3):
                vertex_idx = face[i]
                tex_idx = tex_indices[i] if i < len(tex_indices) else 0
                
                key = (vertex_idx, tex_idx)
                if key not in vertex_map:
                    # 添加新顶点
                    new_vertex_idx = len(vertex_buffer) // 8  # 每个顶点8个分量 (x,y,z,x_rec,y_rec,z_rec,u,v)
                    vertex_map[key] = new_vertex_idx
                    
                    # 添加顶点数据
                    vertex_0 = vertices_0[vertex_idx]
                    vertex_1 = vertices_1[vertex_idx]
                    tex_coord = texture_coords[tex_idx] if tex_idx < len(texture_coords) else [0.0, 0.0]
                    vertex_buffer.extend([vertex_0[0], vertex_0[1], vertex_0[2], vertex_1[0], vertex_1[1], vertex_1[2], tex_coord[0], tex_coord[1]])
                
                face_indices.append(vertex_map[key])
            
            new_indices.extend(face_indices)
        
        return np.array(vertex_buffer, dtype=np.float32), np.array(new_indices, dtype=np.uint32)
    
    def get_bounding_box(self, vertices: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """获取模型的包围盒"""
        min_coords = np.min(vertices, axis=0)
        max_coords = np.max(vertices, axis=0)
        return min_coords, max_coords
    
    def center_and_scale_model(self, vertices: np.ndarray, target_size: float = 1.0) -> np.ndarray:
        """将模型居中并缩放到指定大小"""
        min_coords, max_coords = self.get_bounding_box(vertices)
        center = (min_coords + max_coords) / 2.0
        size = np.max(max_coords - min_coords)
        
        # 居中
        centered_vertices = vertices - center
        
        # 缩放
        if size > 0:
            scale = target_size / size
            centered_vertices *= scale
            
        return centered_vertices
