# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a 3D sticker application that renders 3D facial models with interactive sticker placement. The project implements two sticker modes:
1. **Projection-based stickers** - Uses 3D projection to place stickers in world space
2. **UV texture mapping stickers** - Places stickers directly in UV coordinate space

## Running the Application

The project has two main test files that can be run directly:

```bash
# Run projection-based sticker test
python test_sticker_projection.py

# Run UV texture mapping sticker test  
python test_sticker_texture_mapping.py
```

## Dependencies

This project requires:
- OpenGL 3.3+ support
- Python packages: `numpy`, `glfw`, `PyOpenGL`, `PIL`, `glm`
- 3D models in OBJ format with UV coordinates
- Assets in the `asset/` directory

## Code Architecture

### Core Components

1. **Rendering Pipeline** (`glrender.py`)
   - `ShaderProgram`: Manages OpenGL shader compilation and uniform setting
   - `RenderPass`: High-level rendering abstraction with vertex/index buffer management
   - `FrameBuffer`: Off-screen rendering support

2. **3D Model Loading** (`obj_loader.py`)
   - `OBJLoader`: Loads OBJ files with vertices, texture coordinates, and faces
   - Creates vertex buffers with interleaved data (position + UV coordinates)
   - Supports dual vertex sets for template/reconstructed model comparison

3. **Matrix Mathematics** (`matrix_utils.py`)
   - `MatrixUtils`: 3D transformation matrices using PyGLM for OpenGL compatibility
   - Handles perspective/orthographic projections, view matrices, and sticker projections

4. **Sticker Systems** (`texture_utils.py`)
   - `StickerProjector`: 3D projection-based sticker placement with world space positioning
   - `TextureMappingSticker`: UV-space sticker placement with 2D transform matrices
   - `TextureUtils`: Texture loading and management utilities

5. **User Interaction** (in test files)
   - `MouseController`: Handles camera controls and sticker manipulation
   - Camera orbiting with middle mouse, panning with right mouse
   - Sticker positioning with left mouse, sizing with Shift+left mouse

### Shader Architecture

Shaders are located in `glsl/` directory:
- `model.vert/.frag`: Basic model rendering with projection-based stickers
- `model_texture_mapping.vert/.frag`: Model rendering with UV-space stickers
- `axis.vert/.frag`: Coordinate axis rendering

### Asset Structure

The `asset/` directory contains:
- 3D face models (`.obj` files) with UV coordinates
- Base textures (UV layout images)
- Sticker images (PNG format with transparency)

## Development Notes

- Chinese comments are authoritative per LEGAL.md
- The project uses column-major matrices compatible with OpenGL
- Supports both template and reconstructed face models
- Interactive controls print sticker position/size info with 'P' key
- No build system - direct Python execution