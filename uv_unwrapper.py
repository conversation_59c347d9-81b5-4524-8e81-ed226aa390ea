import pymeshlab
import argparse
import os

def unwrap_3d_model(input_path, output_path, method='lscm'):
    """
    使用PyMeshLab对3D模型进行UV展开
    
    Args:
        input_path (str): 输入OBJ文件路径
        output_path (str): 输出OBJ文件路径
        method (str): UV展开方法
    """
    # 创建MeshSet对象
    ms = pymeshlab.MeshSet()
    
    # 加载模型
    print(f"Loading model from {input_path}")
    ms.load_new_mesh(input_path)
    
    # 根据选择的方法进行UV展开
    if method == 'lscm':
        # 使用最小二乘共形映射(LSCM)方法
        print("Applying LSCM UV parameterization...")
        ms.compute_texcoord_parametrization_least_squares_conformal_maps()
    elif method == 'harmonic':
        # 使用调和映射方法
        print("Applying harmonic UV parameterization...")
        ms.compute_texcoord_parametrization_harmonic()
    elif method == 'flat_plane':
        # 使用平面投影方法
        print("Applying flat plane UV parameterization...")
        ms.compute_texcoord_parametrization_flat_plane_per_wedge()
    elif method == 'cylindrical':
        # 使用圆柱形展开方法
        print("Applying cylindrical UV parameterization...")
        ms.generate_cylindrical_unwrapping()
    elif method == 'voronoi_atlas':
        # 使用Voronoi图集方法
        print("Applying Voronoi atlas UV parameterization...")
        ms.generate_voronoi_atlas_parametrization()
    elif method == 'iso_parametrization':
        # 使用等参数化图集方法
        print("Applying iso parametrization atlas UV parameterization...")
        ms.generate_iso_parametrization_atlased_mesh()
    elif method == 'triangle_trivial':
        # 使用简单的三角面片展开方法
        print("Applying triangle trivial UV parameterization...")
        ms.compute_texcoord_parametrization_triangle_trivial_per_wedge()
    else:
        raise ValueError(f"Unsupported method: {method}")
    
    # 保存结果
    print(f"Saving unwrapped model to {output_path}")
    ms.save_current_mesh(output_path)
    print("UV unwrapping completed successfully!")

def main():
    parser = argparse.ArgumentParser(description="UV Unwrap a 3D model using PyMeshLab")
    parser.add_argument("input", help="Input OBJ file path")
    parser.add_argument("output", help="Output OBJ file path")
    parser.add_argument("--method", 
                        choices=['lscm', 'harmonic', 'flat_plane', 'cylindrical', 
                                'voronoi_atlas', 'iso_parametrization', 'triangle_trivial'], 
                        default='lscm', help="UV unwrapping method")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"Error: Input file {args.input} does not exist")
        return
    
    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    try:
        unwrap_3d_model(args.input, args.output, args.method)
    except Exception as e:
        print(f"Error during UV unwrapping: {e}")

if __name__ == "__main__":
    main()