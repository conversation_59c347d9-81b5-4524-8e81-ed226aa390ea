#version 330 core
precision highp float;

in vec2 v_texCoord;
in vec3 v_worldPos_template;
in vec3 v_worldPos;

uniform sampler2D u_texture;
uniform sampler2D u_stickerTexture;
uniform bool u_hasTexture;
uniform bool u_hasSticker;

// 纹理贴图参数
uniform mat3 u_stickerTransformMatrix;  // UV空间的变换矩阵
uniform float u_stickerAlpha;           // 贴纸透明度

out vec4 FragColor;

void main() {
    vec4 baseColor = vec4(0.8, 0.8, 0.8, 1.0);
    
    // 基础纹理
    if (u_hasTexture) {
        baseColor = texture(u_texture, v_texCoord);
    }
    
    // 纹理贴图贴纸
    if (u_hasSticker) {
        // 将当前UV坐标转换到贴纸局部坐标系
        vec3 uvHomogeneous = vec3(v_texCoord, 1.0);
        
        // 应用逆变换矩阵，将世界UV坐标转换到贴纸局部坐标
        mat3 invTransform = inverse(u_stickerTransformMatrix);
        vec3 localUV = invTransform * uvHomogeneous;
        
        // 转换回2D UV坐标
        vec2 stickerUV = localUV.xy;
        
        // 检查是否在贴纸范围内 ([-0.5, 0.5] -> [0, 1])
        vec2 normalizedUV = stickerUV + vec2(0.5, 0.5);
        
        if (normalizedUV.x >= 0.0 && normalizedUV.x <= 1.0 && 
            normalizedUV.y >= 0.0 && normalizedUV.y <= 1.0) {
            
            vec4 stickerColor = texture(u_stickerTexture, normalizedUV);
            
            // 应用贴纸透明度
            float finalAlpha = stickerColor.a * u_stickerAlpha;
            
            // 混合贴纸颜色到基础颜色
            baseColor.rgb = mix(baseColor.rgb, stickerColor.rgb, finalAlpha);
        }
    }
    
    FragColor = baseColor;
}