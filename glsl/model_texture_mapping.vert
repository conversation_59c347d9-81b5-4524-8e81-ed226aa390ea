#version 330 core
precision highp float;

layout (location = 0) in vec3 a_position_template;
layout (location = 1) in vec3 a_position;
layout (location = 2) in vec2 a_texCoord;

uniform mat4 u_mvpMatrix;
uniform mat4 u_modelMatrix;
uniform mat4 u_viewMatrix;
uniform mat4 u_projectionMatrix;

out vec2 v_texCoord;
out vec3 v_worldPos_template;
out vec3 v_worldPos;

void main()
{
    vec4 worldPos_template = u_modelMatrix * vec4(a_position_template, 1.0);
    vec4 worldPos = u_modelMatrix * vec4(a_position, 1.0);
    
    gl_Position = u_mvpMatrix * vec4(a_position, 1.0);
    
    v_texCoord = a_texCoord;
    v_worldPos_template = worldPos_template.xyz;
    v_worldPos = worldPos.xyz;
}