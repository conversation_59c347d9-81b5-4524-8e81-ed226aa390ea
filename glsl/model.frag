#version 330 core
precision highp float;

in vec2 v_texCoord;
in vec3 v_worldPos_template;
in vec3 v_worldPos;
// in vec3 v_viewPos;

uniform sampler2D u_texture;
uniform sampler2D u_stickerTexture;
uniform bool u_hasTexture;
uniform bool u_hasSticker;

// 投影参数
uniform mat4 u_stickerProjectionMatrix;
uniform vec3 u_stickerPosition;
uniform vec3 u_stickerNormal;
uniform float u_stickerSize;
uniform float u_stickerFadeDistance;

out vec4 FragColor;

void main() {
    vec4 baseColor = vec4(0.8, 0.8, 0.8, 1.0);
    
    // 基础纹理
    if (u_hasTexture) {
        baseColor = texture(u_texture, v_texCoord);
    }
    
    // 贴纸投影
    if (u_hasSticker) {
        // 计算世界坐标到贴纸投影空间的变换
        vec4 stickerPos = u_stickerProjectionMatrix * vec4(v_worldPos_template, 1.0);
        
        // 透视除法
        vec3 projCoords = stickerPos.xyz / stickerPos.w;
        
        // 转换到纹理坐标 [0, 1]
        vec2 stickerUV = projCoords.xy * 0.5 + 0.5;
        
        // 检查是否在投影范围内
        if (stickerUV.x >= 0.0 && stickerUV.x <= 1.0 && 
            stickerUV.y >= 0.0 && stickerUV.y <= 1.0 && 
            projCoords.z >= -1.0 && projCoords.z <= 1.0) {
            
            vec4 stickerColor = texture(u_stickerTexture, stickerUV);
            
            baseColor.rgb = mix(baseColor.rgb, stickerColor.rgb, stickerColor.a);
        }
    }
    
    FragColor = baseColor;
}
