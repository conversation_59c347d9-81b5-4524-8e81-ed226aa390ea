import os
import sys
import numpy as np
import glfw
import math
from OpenGL.GL import *
from glrender import RenderPass
from obj_loader import OBJLoader
from matrix_utils import MatrixUtils
from texture_utils import TextureMappingSticker, TextureUtils
from axis_renderer import AxisRenderer

class MouseController:
    """鼠标触摸板控制 (纹理贴图版本)"""

    def __init__(self, distance=1.0, model_scale=1.0, target=None):
        self.distance = distance
        self.model_scale = model_scale
        self.target = np.array([0.0, 0.0, 0.0]) if target is None else np.array(target)
        self.azimuth = np.pi/2  # 水平角度（绕Y轴）
        self.elevation = 0.0  # 垂直角度（绕X轴）

        # 鼠标状态
        self.last_mouse_x = 0.0
        self.last_mouse_y = 0.0
        self.mouse_sensitivity = 0.005
        self.pan_sensitivity = 0.002
        self.sticker_sensitivity = 0.001
        self.sticker_scale_sensitivity = 0.002  # 贴纸大小调整敏感度
        self.zoom_sensitivity = 0.05
        self.is_rotating = False
        self.is_panning = False
        self.is_sticker_panning = False
        self.is_sticker_scaling = False  # 是否正在调整贴纸大小

        # 距离限制
        self.min_distance = 0.5
        self.max_distance = 5.0

        # 纹理贴图贴纸
        self.texture_mapping_sticker = None

        # 触摸板滚动检测
        self.scroll_history = []  # 存储最近的滚动事件
        self.scroll_history_size = 5  # 保持最近5个事件
        self.trackpad_threshold = 0.1  # 触摸板滚动值阈值

    def set_texture_mapping_sticker(self, sticker):
        """设置纹理贴图贴纸引用"""
        self.texture_mapping_sticker = sticker

    def get_camera_position(self):
        """根据球坐标计算相机位置"""
        # 球坐标转换为笛卡尔坐标
        x = self.distance * math.cos(self.elevation) * math.cos(self.azimuth)
        y = self.distance * math.sin(self.elevation)
        z = self.distance * math.cos(self.elevation) * math.sin(self.azimuth)

        return self.target + np.array([x, y, z])

    def get_view_matrix(self):
        """获取视图矩阵"""
        camera_pos = self.get_camera_position()
        up_vector = np.array([0.0, 1.0, 0.0])
        return MatrixUtils.look_at(camera_pos, self.target, up_vector)

    def get_camera_right_vector(self):
        """获取相机右方向向量"""
        camera_pos = self.get_camera_position()
        camera_dir = self.target - camera_pos
        camera_dir = camera_dir / np.linalg.norm(camera_dir)
        up_vector = np.array([0.0, 1.0, 0.0])
        right_vector = np.cross(up_vector, camera_dir)
        right_vector = right_vector / np.linalg.norm(right_vector)
        return right_vector

    def get_camera_up_vector(self):
        """获取相机上方向向量（在相机平面内）"""
        camera_pos = self.get_camera_position()
        camera_dir = self.target - camera_pos
        camera_dir = camera_dir / np.linalg.norm(camera_dir)
        right_vector = self.get_camera_right_vector()
        up_vector = np.cross(camera_dir, right_vector)
        up_vector = up_vector / np.linalg.norm(up_vector)
        return up_vector

    def handle_mouse_button(self, window, button, action, mods):
        """处理鼠标按键事件"""
        if button == glfw.MOUSE_BUTTON_MIDDLE:
            if action == glfw.PRESS:
                # 中键旋转
                self.is_rotating = True
                self.last_mouse_x, self.last_mouse_y = glfw.get_cursor_pos(window)
            elif action == glfw.RELEASE:
                self.is_rotating = False
        elif button == glfw.MOUSE_BUTTON_RIGHT:
            if action == glfw.PRESS:
                # 右键平移
                self.is_panning = True
                self.last_mouse_x, self.last_mouse_y = glfw.get_cursor_pos(window)
            elif action == glfw.RELEASE:
                self.is_panning = False
        elif button == glfw.MOUSE_BUTTON_LEFT:
            if action == glfw.PRESS:
                # 检查是否按住Shift键
                if mods & glfw.MOD_SHIFT:
                    # Shift+左键调整贴纸大小
                    self.is_sticker_scaling = True
                    self.last_mouse_x, self.last_mouse_y = glfw.get_cursor_pos(window)
                else:
                    # 左键平移贴纸
                    self.is_sticker_panning = True
                    self.last_mouse_x, self.last_mouse_y = glfw.get_cursor_pos(window)
            elif action == glfw.RELEASE:
                self.is_sticker_panning = False
                self.is_sticker_scaling = False

    def handle_mouse_move(self, window, xpos, ypos):
        """处理鼠标移动事件"""
        if self.is_rotating:
            # 旋转相机
            dx = xpos - self.last_mouse_x
            dy = ypos - self.last_mouse_y

            # 更新角度
            self.azimuth += dx * self.mouse_sensitivity
            self.elevation += dy * self.mouse_sensitivity 

            # 限制垂直角度范围
            self.elevation = max(-math.pi/2 + 0.01, min(math.pi/2 - 0.01, self.elevation))

            self.last_mouse_x = xpos
            self.last_mouse_y = ypos

        elif self.is_panning:
            # 平移相机
            dx = xpos - self.last_mouse_x
            dy = ypos - self.last_mouse_y

            # 获取相机平面内的右方向和上方向向量
            right_vector = self.get_camera_right_vector()
            up_vector = self.get_camera_up_vector()

            # 计算平移量
            pan_amount_right = dx * self.pan_sensitivity * self.distance
            pan_amount_up = dy * self.pan_sensitivity * self.distance

            # 更新目标位置
            self.target += right_vector * pan_amount_right + up_vector * pan_amount_up

            self.last_mouse_x = xpos
            self.last_mouse_y = ypos

        elif self.is_sticker_panning and self.texture_mapping_sticker is not None:
            # 平移贴纸 (在UV空间中)
            dx = xpos - self.last_mouse_x
            dy = ypos - self.last_mouse_y

            # 计算UV空间的平移量
            uv_move_scale = self.sticker_sensitivity * 1.2
            move_u = dx * uv_move_scale
            move_v = -dy * uv_move_scale  # 翻转Y轴

            # 获取当前贴纸UV位置
            current_uv = self.texture_mapping_sticker.uv_position

            # 更新贴纸UV位置，限制在[0,1]范围内
            new_u = max(0.0, min(1.0, current_uv[0] + move_u))
            new_v = max(0.0, min(1.0, current_uv[1] + move_v))

            self.texture_mapping_sticker.set_uv_position(new_u, new_v)

            self.last_mouse_x = xpos
            self.last_mouse_y = ypos

        elif self.is_sticker_scaling and self.texture_mapping_sticker is not None:
            # 调整贴纸大小 (在UV空间中)
            dx = xpos - self.last_mouse_x
            dy = ypos - self.last_mouse_y

            # 使用垂直鼠标移动来调整大小（向上增大，向下减小）
            scale_delta = -dy * self.sticker_scale_sensitivity * 0.5

            # 获取当前贴纸大小
            current_size = self.texture_mapping_sticker.uv_size

            # 计算新的大小
            new_size = current_size + scale_delta

            # 设置新的大小（TextureMappingSticker会自动限制范围）
            self.texture_mapping_sticker.set_uv_size(new_size)

            self.last_mouse_x = xpos
            self.last_mouse_y = ypos

    def handle_scroll(self, window, xoffset, yoffset):
        """处理滚轮事件（缩放和旋转）"""
        import time

        # 记录滚动事件
        current_time = time.time()
        self.scroll_history.append({
            'time': current_time,
            'xoffset': xoffset,
            'yoffset': yoffset
        })

        # 保持历史记录大小
        if len(self.scroll_history) > self.scroll_history_size:
            self.scroll_history.pop(0)

        # 检测是否为Mac触摸板滚动
        is_trackpad = self._is_trackpad_scroll()

        if sys.platform == 'darwin' and is_trackpad:
            # Mac触摸板滚动：像中键拖动一样进行旋转
            rotation_sensitivity = self.mouse_sensitivity * 20.0  # 调整敏感度

            if abs(xoffset) > 0.01:
                self.azimuth += xoffset * rotation_sensitivity

            if abs(yoffset) > 0.01:
                self.elevation += yoffset * rotation_sensitivity
                self.elevation = max(-math.pi/2 + 0.01, min(math.pi/2 - 0.01, self.elevation))
        else:
            # 鼠标滚轮或其他情况：缩放
            zoom_factor = 1.0 - yoffset * self.zoom_sensitivity
            self.distance *= zoom_factor
            # 限制距离范围
            self.distance = max(self.min_distance, min(self.max_distance, self.distance))

    def _is_trackpad_scroll(self):
        """检测是否为触摸板滚动"""
        if len(self.scroll_history) < 2:
            return False

        # 检查最近的滚动事件
        recent_events = self.scroll_history[-3:] if len(self.scroll_history) >= 3 else self.scroll_history

        y_values = [abs(event['yoffset']) for event in recent_events]
        x_values = [abs(event['xoffset']) for event in recent_events]
        avg_y_value = sum(y_values) / len(y_values) if y_values else 0
        avg_x_value = sum(x_values) / len(x_values) if x_values else 0

        # 特征1：有水平滚动分量（触摸板支持水平滚动）
        has_horizontal = avg_x_value > 0.01

        # 特征2：垂直滚动值较小（触摸板通常产生小于阈值的值）
        is_small_values = avg_y_value < self.trackpad_threshold

        return has_horizontal or is_small_values
    
    def handle_key(self, window, key, scancode, action, mods):
        """处理键盘事件"""
        if key == glfw.KEY_P and action == glfw.PRESS:
            self.print_sticker_info()

    def print_sticker_info(self):
        """打印贴纸位置和大小信息"""
        if self.texture_mapping_sticker is not None:
            uv_pos = self.texture_mapping_sticker.uv_position
            uv_size = self.texture_mapping_sticker.uv_size

            print("\n=== 纹理贴图贴纸信息 ===")
            print("位置: " + f"{uv_pos[0]:.4f}, {uv_pos[1]:.4f}")
            print("大小: " + f"{uv_size:.4f}")
            print("========================\n")
        else:
            print("纹理贴图贴纸未初始化")

def init_glfw():
    if not glfw.init():
        return False
    
    glfw.window_hint(glfw.CONTEXT_VERSION_MAJOR, 3)
    glfw.window_hint(glfw.CONTEXT_VERSION_MINOR, 3)
    glfw.window_hint(glfw.OPENGL_PROFILE, glfw.OPENGL_CORE_PROFILE)
    if sys.platform == 'darwin':
        glfw.window_hint(glfw.OPENGL_FORWARD_COMPAT, GL_TRUE)
    
    return True

def main():
    if not init_glfw():
        print("Failed to initialize GLFW")
        return

    width, height = 800, 800
    window = glfw.create_window(width, height, "Texture Mapping Sticker Test", None, None)
    if not window:
        print("Failed to create GLFW window")
        glfw.terminate()
        return

    glfw.make_context_current(window)

    USE_DOUYIN_MODEL = False # 是否使用抖音模型

    if USE_DOUYIN_MODEL:
        model_scale = 0.1
        camera_distance = 3.5
        model_template_path = "asset/douyin_decal.obj"
        model_reconstruct_path = "asset/douyin_decal.obj"
        base_texture_path = "asset/douyin_decal_UV_layout.png"
    else:
        model_scale = 5.0
        camera_distance = 2.0
        model_template_path = "asset/head_template_face_decimate.obj"
        model_reconstruct_path = "asset/face_decimate_reconstruct_c.obj"
        base_texture_path = "asset/head_template_face_decimate_UV_layout.png"

    USE_PERSPECTIVE = True # 是否使用透视投影

    # 创建鼠标控制器
    mouse_controller = MouseController(distance=camera_distance, model_scale=model_scale)

    # 设置鼠标回调
    glfw.set_mouse_button_callback(window, mouse_controller.handle_mouse_button)
    glfw.set_cursor_pos_callback(window, mouse_controller.handle_mouse_move)
    glfw.set_scroll_callback(window, mouse_controller.handle_scroll)

    # 设置键盘回调
    glfw.set_key_callback(window, mouse_controller.handle_key)
    
    # 启用深度测试
    glEnable(GL_DEPTH_TEST)
    glEnable(GL_BLEND)
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
    
    try:
        print("Loading OBJ model...")
        obj_loader = OBJLoader()
        vertices_template, texture_coords, indices = obj_loader.load(model_template_path) # 模板模型
        vertices, _, _ = obj_loader.load(model_reconstruct_path) # 重建模型
        vertices = vertices_template # 贴到标准脸上
        
        print(f"Loaded {len(vertices_template)} vertices, {len(texture_coords)} texture coordinates, {len(indices)//3} faces")
        
        vertex_buffer, indices = obj_loader.create_vertex_buffer_with_texcoords(vertices_template, vertices, texture_coords, indices)
        
        # 使用纹理贴图着色器
        render_pass = RenderPass("glsl/model_texture_mapping.vert", "glsl/model_texture_mapping.frag")
        
        # 设置顶点数据布局：位置(3) + 纹理坐标(2)
        layout = [(3, GL_FLOAT), (3, GL_FLOAT), (2, GL_FLOAT)]
        render_pass.update_vertex_data(vertex_buffer, layout)
        render_pass.update_index_data(indices)
        
        print("Loading base texture...")
        base_texture_id = TextureUtils.load_texture_from_file(base_texture_path)

        # 创建纹理贴图贴纸
        texture_mapping_sticker = TextureMappingSticker()
        texture_mapping_sticker.load_texture("asset/Blender_logo_no_text.svg.png")

        # 设置贴纸参数
        texture_mapping_sticker.set_uv_position(0.5, 0.5)  # UV坐标系中的位置
        texture_mapping_sticker.set_uv_size(0.2)           # UV坐标系中的大小
        texture_mapping_sticker.set_alpha(1.0)             # 透明度

        mouse_controller.set_texture_mapping_sticker(texture_mapping_sticker)

        axis_renderer = AxisRenderer(axis_length=0.5)

        print("\nControl Instructions:")
        print("- Hold LEFT mouse button and drag to move sticker position in UV space")
        print("- Hold SHIFT + LEFT mouse button and drag to adjust sticker size")
        print("- Hold MIDDLE mouse button and drag to rotate camera around model")
        print("- Hold RIGHT mouse button and drag to pan camera")
        print("- Use mouse wheel to zoom in/out")
        print("- Press 'P' to print current sticker UV position and size")
        print("- Press ESC to exit")
        print()

        while not glfw.window_should_close(window):
            fb_width, fb_height = glfw.get_framebuffer_size(window)
            glViewport(0, 0, fb_width, fb_height)
            glClearColor(0.2, 0.2, 0.2, 1.0)
            glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)

            model_matrix = MatrixUtils.scale(model_scale, model_scale, model_scale)

            view_matrix = mouse_controller.get_view_matrix()

            aspect_ratio = fb_width / fb_height if fb_height > 0 else 1.0
            projection_matrix = MatrixUtils.perspective(math.radians(45.0), aspect_ratio, 0.1, 100.0) if USE_PERSPECTIVE else MatrixUtils.orthographic(-1.0, 1.0, -1.0, 1.0, -10.0, 10.0)
            
            # MVP矩阵
            mvp_matrix = MatrixUtils.multiply(projection_matrix, MatrixUtils.multiply(view_matrix, model_matrix))
            
            render_pass.set_uniform("u_modelMatrix", model_matrix, 'matrix4fv')
            render_pass.set_uniform("u_viewMatrix", view_matrix, 'matrix4fv')
            render_pass.set_uniform("u_projectionMatrix", projection_matrix, 'matrix4fv')
            render_pass.set_uniform("u_mvpMatrix", mvp_matrix, 'matrix4fv')
            
            render_pass.set_uniform("u_hasTexture", 1, '1i')
            render_pass.set_sampler("u_texture", base_texture_id)
            
            # 设置纹理贴图贴纸
            render_pass.set_uniform("u_hasSticker", 1, '1i')
            render_pass.set_sampler("u_stickerTexture", texture_mapping_sticker.texture_id)
            render_pass.set_uniform("u_stickerTransformMatrix", texture_mapping_sticker.get_transform_matrix(), 'matrix3fv')
            render_pass.set_uniform("u_stickerAlpha", texture_mapping_sticker.alpha, '1f')
            
            render_pass.render()

            # 渲染坐标轴（固定在屏幕右上角）
            axis_scale = 0.2  # 坐标轴缩放
            axis_mvp_matrix = projection_matrix @ MatrixUtils.translate(0.65, 0.65, 0.0) @ view_matrix @ MatrixUtils.scale(axis_scale, axis_scale, axis_scale)

            axis_renderer.render(axis_mvp_matrix)

            glfw.swap_buffers(window)
            glfw.poll_events()
            
            if glfw.get_key(window, glfw.KEY_ESCAPE) == glfw.PRESS:
                break
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'texture_mapping_sticker' in locals():
            texture_mapping_sticker.cleanup()
        if 'base_texture_id' in locals():
            TextureUtils.delete_texture(base_texture_id)
        glfw.terminate()

if __name__ == '__main__':
    main()