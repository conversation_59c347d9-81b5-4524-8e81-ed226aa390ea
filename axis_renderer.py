import numpy as np
from OpenGL.GL import *
from glrender import RenderPass

class AxisRenderer:
    """坐标轴渲染器"""

    def __init__(self, axis_length=1.0):
        self.axis_length = axis_length
        self.line_render_pass = None
        self.text_render_pass = None
        self.setup()

    def setup(self):
        """设置坐标轴渲染器"""
        # 创建线条渲染通道
        self.line_render_pass = RenderPass("glsl/axis.vert", "glsl/axis.frag")

        # 创建文字渲染通道
        self.text_render_pass = RenderPass("glsl/axis_text.vert", "glsl/axis_text.frag")

        # 创建坐标轴线条顶点数据
        self._setup_axis_lines()

        # 创建文字标签顶点数据
        self._setup_text_labels()

    def _setup_axis_lines(self):
        """设置坐标轴线条"""
        # 顶点格式：[x, y, z, r, g, b]
        arrow_size = self.axis_length * 0.08  # 箭头大小

        vertices = []
        indices = []
        vertex_index = 0

        # 使用更亮的颜色，在深色背景下更清晰
        # X轴 (亮红色)
        x_color = [1.0, 0.3, 0.3]  # 更亮的红色
        # 主轴线
        vertices.extend([0.0, 0.0, 0.0] + x_color)  # 原点
        vertices.extend([self.axis_length, 0.0, 0.0] + x_color)  # X轴端点
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # X轴箭头
        vertices.extend([self.axis_length, 0.0, 0.0] + x_color)  # 箭头顶点
        vertices.extend([self.axis_length - arrow_size, arrow_size, 0.0] + x_color)  # 箭头边1
        vertices.extend([self.axis_length - arrow_size, -arrow_size, 0.0] + x_color)  # 箭头边2
        indices.extend([vertex_index, vertex_index + 1])
        indices.extend([vertex_index, vertex_index + 2])
        vertex_index += 3

        # Y轴 (亮绿色)
        y_color = [0.3, 1.0, 0.3]  # 更亮的绿色
        # 主轴线
        vertices.extend([0.0, 0.0, 0.0] + y_color)  # 原点
        vertices.extend([0.0, self.axis_length, 0.0] + y_color)  # Y轴端点
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # Y轴箭头
        vertices.extend([0.0, self.axis_length, 0.0] + y_color)  # 箭头顶点
        vertices.extend([arrow_size, self.axis_length - arrow_size, 0.0] + y_color)  # 箭头边1
        vertices.extend([-arrow_size, self.axis_length - arrow_size, 0.0] + y_color)  # 箭头边2
        indices.extend([vertex_index, vertex_index + 1])
        indices.extend([vertex_index, vertex_index + 2])
        vertex_index += 3

        # Z轴 (亮蓝色)
        z_color = [0.3, 0.3, 1.0]  # 更亮的蓝色
        # 主轴线
        vertices.extend([0.0, 0.0, 0.0] + z_color)  # 原点
        vertices.extend([0.0, 0.0, self.axis_length] + z_color)  # Z轴端点
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # Z轴箭头
        vertices.extend([0.0, 0.0, self.axis_length] + z_color)  # 箭头顶点
        vertices.extend([0.0, arrow_size, self.axis_length - arrow_size] + z_color)  # 箭头边1
        vertices.extend([0.0, -arrow_size, self.axis_length - arrow_size] + z_color)  # 箭头边2
        indices.extend([vertex_index, vertex_index + 1])
        indices.extend([vertex_index, vertex_index + 2])

        vertices = np.array(vertices, dtype=np.float32)
        indices = np.array(indices, dtype=np.uint32)

        # 设置顶点数据布局：位置(3) + 颜色(3)
        layout = [(3, GL_FLOAT), (3, GL_FLOAT)]
        self.line_render_pass.update_vertex_data(vertices, layout)
        self.line_render_pass.update_index_data(indices)

    def _setup_text_labels(self):
        """设置文字标签"""
        # 创建简单的文字标签（使用线条绘制字母）
        vertices = []
        indices = []
        vertex_index = 0

        label_size = self.axis_length * 0.05  # 文字大小
        label_offset = self.axis_length * 0.15  # 文字偏移距离

        # X标签 (在X轴末端)
        x_pos = [self.axis_length + label_offset, 0.0, 0.0]
        x_color = [1.0, 0.5, 0.5]  # 稍微柔和的红色

        # 绘制字母X (两条对角线)
        # 第一条对角线：左上到右下
        vertices.extend([x_pos[0] - label_size, x_pos[1] + label_size, x_pos[2]] + x_color)
        vertices.extend([x_pos[0] + label_size, x_pos[1] - label_size, x_pos[2]] + x_color)
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # 第二条对角线：左下到右上
        vertices.extend([x_pos[0] - label_size, x_pos[1] - label_size, x_pos[2]] + x_color)
        vertices.extend([x_pos[0] + label_size, x_pos[1] + label_size, x_pos[2]] + x_color)
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # Y标签 (在Y轴末端)
        y_pos = [0.0, self.axis_length + label_offset, 0.0]
        y_color = [0.5, 1.0, 0.5]  # 稍微柔和的绿色

        # 绘制字母Y
        # 左上到中心
        vertices.extend([y_pos[0] - label_size, y_pos[1] + label_size, y_pos[2]] + y_color)
        vertices.extend([y_pos[0], y_pos[1], y_pos[2]] + y_color)
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # 右上到中心
        vertices.extend([y_pos[0] + label_size, y_pos[1] + label_size, y_pos[2]] + y_color)
        vertices.extend([y_pos[0], y_pos[1], y_pos[2]] + y_color)
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # 中心到下方
        vertices.extend([y_pos[0], y_pos[1], y_pos[2]] + y_color)
        vertices.extend([y_pos[0], y_pos[1] - label_size, y_pos[2]] + y_color)
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # Z标签 (在Z轴末端)
        z_pos = [0.0, 0.0, self.axis_length + label_offset]
        z_color = [0.5, 0.5, 1.0]  # 稍微柔和的蓝色

        # 绘制字母Z
        # 上横线
        vertices.extend([z_pos[0] - label_size, z_pos[1] + label_size, z_pos[2]] + z_color)
        vertices.extend([z_pos[0] + label_size, z_pos[1] + label_size, z_pos[2]] + z_color)
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # 对角线
        vertices.extend([z_pos[0] + label_size, z_pos[1] + label_size, z_pos[2]] + z_color)
        vertices.extend([z_pos[0] - label_size, z_pos[1] - label_size, z_pos[2]] + z_color)
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        # 下横线
        vertices.extend([z_pos[0] - label_size, z_pos[1] - label_size, z_pos[2]] + z_color)
        vertices.extend([z_pos[0] + label_size, z_pos[1] - label_size, z_pos[2]] + z_color)
        indices.extend([vertex_index, vertex_index + 1])
        vertex_index += 2

        vertices = np.array(vertices, dtype=np.float32)
        indices = np.array(indices, dtype=np.uint32)

        # 设置顶点数据布局：位置(3) + 颜色(3)
        layout = [(3, GL_FLOAT), (3, GL_FLOAT)]
        self.text_render_pass.update_vertex_data(vertices, layout)
        self.text_render_pass.update_index_data(indices)
    
    def render(self, mvp_matrix):
        """渲染坐标轴

        Args:
            mvp_matrix: 模型-视图-投影矩阵
        """
        # 查询支持的线宽范围
        line_width_range = glGetFloatv(GL_LINE_WIDTH_RANGE)
        max_line_width = min(5.0, line_width_range[1])  # 增加到5.0，使线条更粗

        # 设置线宽（在支持的范围内）
        if max_line_width > 1.0:
            glLineWidth(max_line_width)

        # 渲染坐标轴线条
        self.line_render_pass.set_uniform("u_mvpMatrix", mvp_matrix, 'matrix4fv')
        self.line_render_pass.render(GL_LINES)

        # 设置文字标签的线宽（稍微细一些）
        text_line_width = min(5.0, line_width_range[1])
        if text_line_width > 1.0:
            glLineWidth(text_line_width)

        # 渲染文字标签
        self.text_render_pass.set_uniform("u_mvpMatrix", mvp_matrix, 'matrix4fv')
        self.text_render_pass.render(GL_LINES)

        # 恢复默认线宽
        if max_line_width > 1.0:
            glLineWidth(1.0)
    
    def cleanup(self):
        """清理资源"""
        # RenderPass会自动清理其资源
        pass
