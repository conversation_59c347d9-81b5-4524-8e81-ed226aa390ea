import numpy as np
from PIL import Image
from OpenGL.GL import *

class TextureUtils:
    """纹理工具类"""
    
    @staticmethod
    def load_texture_from_file(filepath: str) -> int:
        """从文件加载纹理"""
        try:
            image = Image.open(filepath).convert("RGBA")
            # 翻转图像，因为OpenGL坐标系和PIL坐标系相反
            image = image.transpose(Image.FLIP_TOP_BOTTOM)
            
            width, height = image.size
            image_data = np.array(image, dtype=np.uint8)
            
            return TextureUtils.create_texture_from_data(image_data, width, height)
            
        except Exception as e:
            print(f"Failed to load texture from {filepath}: {e}")
            return TextureUtils.create_default_texture()
    
    @staticmethod
    def create_texture_from_data(image_data: np.ndarray, width: int, height: int) -> int:
        """从图像数据创建纹理"""
        # 生成纹理ID
        texture_id = glGenTextures(1)
        glBindTexture(GL_TEXTURE_2D, texture_id)
        
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR)
        
        if image_data.shape[2] == 4:  # RGBA
            glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, image_data)
        elif image_data.shape[2] == 3:  # RGB
            glTexImage2D(GL_TEXTURE_2D, 0, GL_RGB, width, height, 0, GL_RGB, GL_UNSIGNED_BYTE, image_data)
        else:
            raise ValueError("Unsupported image format")
        
        glBindTexture(GL_TEXTURE_2D, 0)
        
        return texture_id
    
    @staticmethod
    def create_default_texture() -> int:
        """创建默认纹理（白色）"""
        white_pixel = np.array([[[255, 255, 255, 255]]], dtype=np.uint8)
        return TextureUtils.create_texture_from_data(white_pixel, 1, 1)
    
    @staticmethod
    def delete_texture(texture_id: int):
        """删除纹理"""
        if texture_id > 0:
            glDeleteTextures(1, [texture_id])

class StickerProjector:
    """贴纸投影器"""
    
    def __init__(self):
        self.position = np.array([0.0, 0.0, 0.1], dtype=np.float32)  # 贴纸投影位置
        self.normal = np.array([0.0, 0.0, -1.0], dtype=np.float32)   # 投影方向
        self.up = np.array([0.0, 1.0, 0.0], dtype=np.float32)       # 上方向
        self.size = 0.2                                              # 贴纸大小
        self.texture_id = 0
        
    def set_position(self, x: float, y: float, z: float):
        """设置投影位置"""
        self.position = np.array([x, y, z], dtype=np.float32)
    
    def set_normal(self, x: float, y: float, z: float):
        """设置投影方向"""
        self.normal = np.array([x, y, z], dtype=np.float32)
        norm = np.linalg.norm(self.normal)
        if norm > 0:
            self.normal /= norm
    
    def set_up(self, x: float, y: float, z: float):
        """设置上方向"""
        self.up = np.array([x, y, z], dtype=np.float32)
        norm = np.linalg.norm(self.up)
        if norm > 0:
            self.up /= norm
    
    def set_size(self, size: float):
        """设置贴纸大小"""
        self.size = size
    
    def load_texture(self, filepath: str):
        """加载贴纸纹理"""
        if self.texture_id > 0:
            TextureUtils.delete_texture(self.texture_id)
        self.texture_id = TextureUtils.load_texture_from_file(filepath)
    
    def get_projection_matrix(self) -> np.ndarray:
        """获取投影矩阵"""
        from matrix_utils import MatrixUtils
        return MatrixUtils.create_projection_matrix(self.position, self.normal, self.up, self.size)
    
    def cleanup(self):
        """清理资源"""
        if self.texture_id > 0:
            TextureUtils.delete_texture(self.texture_id)
            self.texture_id = 0

class TextureMappingSticker:
    """基于纹理贴图的贴纸管理器"""
    
    def __init__(self):
        self.uv_position = np.array([0.5, 0.5], dtype=np.float32)  # UV坐标系中的位置 [0,1] 范围
        self.uv_size = 0.2                                          # UV坐标系中的大小
        self.rotation = 0.0                                         # 旋转角度 (弧度)
        self.texture_id = 0
        self.alpha = 1.0                                           # 透明度
        
    def set_uv_position(self, u: float, v: float):
        """设置UV坐标系中的位置"""
        self.uv_position = np.array([u, v], dtype=np.float32)
    
    def set_uv_size(self, size: float):
        """设置UV坐标系中的大小"""
        self.uv_size = max(0.01, min(1.0, size))  # 限制在合理范围内
    
    def set_rotation(self, angle_rad: float):
        """设置旋转角度"""
        self.rotation = angle_rad
        
    def set_alpha(self, alpha: float):
        """设置透明度"""
        self.alpha = max(0.0, min(1.0, alpha))
    
    def load_texture(self, filepath: str):
        """加载贴纸纹理"""
        if self.texture_id > 0:
            TextureUtils.delete_texture(self.texture_id)
        self.texture_id = TextureUtils.load_texture_from_file(filepath)
    
    def get_transform_matrix(self) -> np.ndarray:
        """获取UV空间的变换矩阵 (3x3)"""
        # 创建变换矩阵：平移 * 旋转 * 缩放
        cos_r = np.cos(self.rotation)
        sin_r = np.sin(self.rotation)
        
        # 缩放矩阵
        scale_matrix = np.array([
            [self.uv_size, 0.0, 0.0],
            [0.0, self.uv_size, 0.0],
            [0.0, 0.0, 1.0]
        ], dtype=np.float32)
        
        # 旋转矩阵
        rotation_matrix = np.array([
            [cos_r, -sin_r, 0.0],
            [sin_r, cos_r, 0.0],
            [0.0, 0.0, 1.0]
        ], dtype=np.float32)
        
        # 平移矩阵
        translation_matrix = np.array([
            [1.0, 0.0, self.uv_position[0]],
            [0.0, 1.0, self.uv_position[1]],
            [0.0, 0.0, 1.0]
        ], dtype=np.float32)
        
        # 组合变换：T * R * S
        return translation_matrix @ rotation_matrix @ scale_matrix
    
    def cleanup(self):
        """清理资源"""
        if self.texture_id > 0:
            TextureUtils.delete_texture(self.texture_id)
            self.texture_id = 0
