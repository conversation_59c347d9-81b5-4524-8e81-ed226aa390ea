import numpy as np
import glm

class MatrixUtils:
    """矩阵工具类，提供常用的3D变换矩阵，使用PyGLM确保与OpenGL列优先矩阵格式一致"""

    @staticmethod
    def identity() -> np.ndarray:
        """创建4x4单位矩阵"""
        return np.array(glm.mat4(), dtype=np.float32)

    @staticmethod
    def translate(x: float, y: float, z: float) -> np.ndarray:
        """创建平移矩阵"""
        return np.array(glm.translate(glm.mat4(), glm.vec3(x, y, z)), dtype=np.float32)

    @staticmethod
    def scale(x: float, y: float, z: float) -> np.ndarray:
        """创建缩放矩阵"""
        return np.array(glm.scale(glm.mat4(), glm.vec3(x, y, z)), dtype=np.float32)
    
    @staticmethod
    def rotate_x(angle_rad: float) -> np.ndarray:
        """创建绕X轴旋转矩阵"""
        return np.array(glm.rotate(glm.mat4(), angle_rad, glm.vec3(1, 0, 0)), dtype=np.float32)

    @staticmethod
    def rotate_y(angle_rad: float) -> np.ndarray:
        """创建绕Y轴旋转矩阵"""
        return np.array(glm.rotate(glm.mat4(), angle_rad, glm.vec3(0, 1, 0)), dtype=np.float32)

    @staticmethod
    def rotate_z(angle_rad: float) -> np.ndarray:
        """创建绕Z轴旋转矩阵"""
        return np.array(glm.rotate(glm.mat4(), angle_rad, glm.vec3(0, 0, 1)), dtype=np.float32)
    
    @staticmethod
    def perspective(fov_rad: float, aspect: float, near: float, far: float) -> np.ndarray:
        """创建透视投影矩阵"""
        return np.array(glm.perspective(fov_rad, aspect, near, far), dtype=np.float32)

    @staticmethod
    def orthographic(left: float, right: float, bottom: float, top: float, near: float, far: float) -> np.ndarray:
        """创建正交投影矩阵"""
        return np.array(glm.ortho(left, right, bottom, top, near, far), dtype=np.float32)
    
    @staticmethod
    def look_at(eye: np.ndarray, target: np.ndarray, up: np.ndarray) -> np.ndarray:
        """创建视图矩阵"""
        eye_vec = glm.vec3(eye[0], eye[1], eye[2])
        target_vec = glm.vec3(target[0], target[1], target[2])
        up_vec = glm.vec3(up[0], up[1], up[2])
        return np.array(glm.lookAt(eye_vec, target_vec, up_vec), dtype=np.float32)
    
    @staticmethod
    def multiply(a: np.ndarray, b: np.ndarray) -> np.ndarray:
        """矩阵乘法"""
        ## 注意：a和b都转换为numpy了，直接用numpy的矩阵乘法即可，转换回glm再计算是错误的
        return a @ b
    
    @staticmethod
    def create_projection_matrix(position: np.ndarray, normal: np.ndarray, up: np.ndarray, size: float) -> np.ndarray:
        """创建贴纸投影矩阵"""
        pos_vec = glm.vec3(position[0], position[1], position[2])
        normal_vec = glm.normalize(glm.vec3(normal[0], normal[1], normal[2]))
        up_vec = glm.vec3(up[0], up[1], up[2])

        target_vec = pos_vec + normal_vec

        # 创建视图矩阵
        view_matrix = glm.lookAt(pos_vec, target_vec, up_vec)

        # 创建正交投影矩阵
        half_size = size / 2.0
        proj_matrix = glm.ortho(-half_size, half_size, -half_size, half_size, -10.0, 10.0)

        result_matrix = proj_matrix * view_matrix

        return np.array(result_matrix, dtype=np.float32)
