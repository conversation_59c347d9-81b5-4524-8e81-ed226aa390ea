import os
import numpy as np
from OpenGL.GL import *
from OpenGL.GL import shaders

class ShaderProgram:
    def __init__(self, vertex_source, fragment_source):
        self.program_id = None
        self.create_program(vertex_source, fragment_source)
    
    def create_program(self, vertex_source, fragment_source):
        vertex_shader = shaders.compileShader(vertex_source, GL_VERTEX_SHADER)
        fragment_shader = shaders.compileShader(fragment_source, GL_FRAGMENT_SHADER)
        
        self.program_id = shaders.compileProgram(vertex_shader, fragment_shader)
    
    def use(self):
        glUseProgram(self.program_id)
    
    def set_uniform_1i(self, name, value):
        location = glGetUniformLocation(self.program_id, name)
        glUniform1i(location, value)
    
    def set_uniform_1f(self, name, value):
        location = glGetUniformLocation(self.program_id, name)
        glUniform1f(location, value)
    
    def set_uniform_2f(self, name, x, y):
        location = glGetUniformLocation(self.program_id, name)
        glUniform2f(location, x, y)
    
    def set_uniform_3f(self, name, x, y, z):
        location = glGetUniformLocation(self.program_id, name)
        glUniform3f(location, x, y, z)
    
    def set_uniform_matrix3fv(self, name, value):
        location = glGetUniformLocation(self.program_id, name)
        glUniformMatrix3fv(location, 1, GL_TRUE, value)
    
    def set_uniform_matrix4fv(self, name, value):
        location = glGetUniformLocation(self.program_id, name)
        glUniformMatrix4fv(location, 1, GL_TRUE, value) # GL使用列优先

class RenderPass:
    def __init__(self, vertex_shader_path, fragment_shader_path):
        self.shader_program = None
        self.vao = None
        self.vbo = None
        self.ebo = None
        self.indices_count = 0
        self.texture_unit_counter = 0
        self.max_texture_units = glGetIntegerv(GL_MAX_TEXTURE_IMAGE_UNITS)
        self.sampler_unit_map = {}  # 跟踪采样器名称到纹理单元的映射
        self.setup_buffers()
        self.load_shaders(vertex_shader_path, fragment_shader_path)
    
    def load_shaders(self, vertex_path, fragment_path):
        with open(vertex_path, 'r') as f:
            vertex_source = f.read()
        with open(fragment_path, 'r') as f:
            fragment_source = f.read()
        
        glBindVertexArray(self.vao)
        self.shader_program = ShaderProgram(vertex_source, fragment_source)
    
    def setup_buffers(self):
        self.vao = glGenVertexArrays(1)
        self.vbo = glGenBuffers(1)
        self.ebo = glGenBuffers(1)
    
    def update_vertex_data(self, vertices, layout):
        """更新顶点数据
        
        Args:
            vertices: 顶点数据数组
            layout: 顶点属性布局，例如：[(3, GL_FLOAT), (2, GL_FLOAT)] 表示位置(3)和纹理坐标(2)
        """
        assert vertices.dtype == np.float32, "Vertices must be float32"
        assert len(layout) > 0, "Layout must not be empty"
        glBindVertexArray(self.vao)
        glBindBuffer(GL_ARRAY_BUFFER, self.vbo)
        glBufferData(GL_ARRAY_BUFFER, vertices.nbytes, vertices, GL_STATIC_DRAW)
        
        stride = sum(attr[0] * 4 for attr in layout)  # 4 bytes per float
        offset = 0
        
        for idx, (size, attr_type) in enumerate(layout):
            glVertexAttribPointer(idx, size, attr_type, GL_FALSE, stride, ctypes.c_void_p(offset))
            glEnableVertexAttribArray(idx)
            offset += size * 4  # 4 bytes per float
    
    def update_index_data(self, indices):
        """更新索引数据
        
        Args:
            indices: 索引数据数组
        """
        assert indices.dtype == np.uint32, "Indices must be uint32"
        glBindVertexArray(self.vao)
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, self.ebo)
        glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.nbytes, indices, GL_STATIC_DRAW)
        self.indices_count = len(indices)
    
    def render(self, primitive_type=GL_TRIANGLES):
        """执行渲染
        
        Args:
            primitive_type: 图元类型，默认为GL_TRIANGLES
        """
        self.shader_program.use()
        glBindVertexArray(self.vao)
        glDrawElements(primitive_type, self.indices_count, GL_UNSIGNED_INT, None)
    
    def set_uniform(self, name, value, uniform_type='1f'):
        """设置uniform变量
        
        Args:
            name: uniform变量名
            value: 要设置的值
            uniform_type: uniform类型，支持'1i', '1f', '2f', '3f', 'matrix3fv', 'matrix4fv'
        """
        self.shader_program.use()
        if uniform_type == '1i':
            self.shader_program.set_uniform_1i(name, value)
        elif uniform_type == '1f':
            self.shader_program.set_uniform_1f(name, value)
        elif uniform_type == '2f':
            self.shader_program.set_uniform_2f(name, *value)
        elif uniform_type == '3f':
            self.shader_program.set_uniform_3f(name, *value)
        elif uniform_type == 'matrix3fv':
            self.shader_program.set_uniform_matrix3fv(name, value)
        elif uniform_type == 'matrix4fv':
            self.shader_program.set_uniform_matrix4fv(name, value)

    def set_sampler(self, sampler_name, texture_id):
        if sampler_name in self.sampler_unit_map:
            texture_unit = self.sampler_unit_map[sampler_name]
        else:
            if self.texture_unit_counter >= self.max_texture_units:
                raise Exception(f"超出最大纹理单元数量限制 ({self.max_texture_units})")
            texture_unit = self.texture_unit_counter
            self.sampler_unit_map[sampler_name] = texture_unit
            self.texture_unit_counter += 1
        
        self.shader_program.use()
        glActiveTexture(GL_TEXTURE0 + texture_unit)
        glBindTexture(GL_TEXTURE_2D, texture_id)
        self.shader_program.set_uniform_1i(sampler_name, texture_unit)

class FrameBuffer:
    def __init__(self, width, height, input_image=None):
        self.width = width
        self.height = height
        self.fbo = None
        self.texture = None
        self.rbo = None
        self.input_image = input_image
        self.setup()
    
    def setup(self):
        self.fbo = glGenFramebuffers(1)
        glBindFramebuffer(GL_FRAMEBUFFER, self.fbo)
        
        self.texture = glGenTextures(1)
        glBindTexture(GL_TEXTURE_2D, self.texture)
        
        if self.input_image is not None:
            # 如果提供了输入图像，使用图像数据创建纹理
            assert self.input_image.shape[2] == 4, "Input image must be RGBA"
            glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, self.width, self.height, 0, GL_RGBA, GL_UNSIGNED_BYTE, self.input_image)
        else:
            # 否则创建空纹理
            glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, self.width, self.height, 0, GL_RGBA, GL_UNSIGNED_BYTE, None)
        
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR)
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR)
        glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, self.texture, 0)
        
        self.rbo = glGenRenderbuffers(1)
        glBindRenderbuffer(GL_RENDERBUFFER, self.rbo)
        glRenderbufferStorage(GL_RENDERBUFFER, GL_DEPTH24_STENCIL8, self.width, self.height)
        glFramebufferRenderbuffer(GL_FRAMEBUFFER, GL_DEPTH_STENCIL_ATTACHMENT, GL_RENDERBUFFER, self.rbo)
        
        if glCheckFramebufferStatus(GL_FRAMEBUFFER) != GL_FRAMEBUFFER_COMPLETE:
            raise Exception("Framebuffer is not complete!")
        
        glBindFramebuffer(GL_FRAMEBUFFER, 0)
    
    def bind(self):
        """绑定帧缓冲以进行渲染"""
        glBindFramebuffer(GL_FRAMEBUFFER, self.fbo)
        glViewport(0, 0, self.width, self.height)
    
    def unbind(self):
        """解绑帧缓冲，切换回默认帧缓冲"""
        glBindFramebuffer(GL_FRAMEBUFFER, 0)
    
    def get_texture(self):
        """获取帧缓冲的纹理附件"""
        return self.texture